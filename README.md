# MQTT Broker 服务器应用程序

## 项目概括
本项目旨在开发一个基于 .NET 8.0+ 的功能完整的 MQTT Broker 服务器应用程序，支持 MQTT 3.1.1 和 MQTT 5.0 协议标准，提供高性能的消息发布订阅服务，适用于物联网设备通信和实时消息传递场景。

## 技术选型
- **主要编程语言**: C# (.NET 8.0+)
- **网络通信**: TCP Socket + ASP.NET Core
- **异步编程**: async/await 模式
- **消息持久化**: Entity Framework Core + SQLite/SQL Server
- **配置管理**: Microsoft.Extensions.Configuration (appsettings.json)
- **日志记录**: Microsoft.Extensions.Logging + Serilog
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **测试框架**: xUnit + Moq
- **性能监控**: System.Diagnostics.Metrics
- **版本控制**: Git

## 项目结构 / 模块划分
```
/MqttBroker/
├── /src/
│   ├── /MqttBroker.Core/              # 核心业务逻辑
│   │   ├── /Protocol/                 # MQTT 协议解析
│   │   ├── /Services/                 # 核心服务
│   │   ├── /Models/                   # 数据模型
│   │   ├── /Interfaces/               # 接口定义
│   │   └── /Utilities/                # 工具类
│   ├── /MqttBroker.Server/            # 服务器主程序
│   │   ├── /Controllers/              # API 控制器
│   │   ├── /Middleware/               # 中间件
│   │   └── /Configuration/            # 配置管理
│   ├── /MqttBroker.Data/              # 数据访问层
│   │   ├── /Entities/                 # 数据实体
│   │   ├── /Repositories/             # 数据仓储
│   │   └── /Context/                  # 数据库上下文
│   └── /MqttBroker.Console/           # 控制台演示程序
├── /tests/
│   ├── /MqttBroker.Core.Tests/        # 核心逻辑测试
│   ├── /MqttBroker.Server.Tests/      # 服务器测试
│   └── /MqttBroker.Integration.Tests/ # 集成测试
├── /docs/                             # 文档目录
├── MqttBroker.sln                     # 解决方案文件
├── appsettings.json                   # 配置文件
└── .gitignore                         # Git 忽略配置
```

## 核心功能 / 模块详解
- **连接管理模块 (ConnectionManager)**: 处理客户端连接、断开连接、心跳检测，维护活跃连接池，支持连接状态监控和超时处理。
- **协议解析模块 (ProtocolHandler)**: 实现 MQTT 3.1.1 和 5.0 协议的数据包解析和构建，包括 CONNECT、PUBLISH、SUBSCRIBE 等消息类型的处理。
- **消息路由模块 (MessageRouter)**: 实现发布订阅消息路由，支持主题过滤器和通配符匹配（+ 单级通配符，# 多级通配符）。
- **QoS 处理模块 (QoSManager)**: 实现 QoS 0（最多一次）、QoS 1（至少一次）、QoS 2（恰好一次）三个服务质量等级的消息传递保证。
- **会话管理模块 (SessionManager)**: 管理客户端会话状态，支持持久会话和清理会话，处理离线消息存储和恢复。
- **认证授权模块 (AuthenticationService)**: 实现基于用户名密码的客户端认证，支持访问控制列表（ACL）和主题权限管理。
- **消息持久化模块 (PersistenceService)**: 负责重要消息、会话状态、订阅信息的数据库存储和恢复，确保服务重启后数据不丢失。
- **监控统计模块 (MetricsService)**: 收集和报告 Broker 运行指标，包括连接数、消息吞吐量、错误率等性能数据。

## 数据模型
- **Client**: { ClientId (PK), Username, Password, IsConnected, LastSeen, CleanSession, KeepAlive }
- **Session**: { SessionId (PK), ClientId (FK), IsPersistent, CreatedAt, LastActivity }
- **Subscription**: { Id (PK), ClientId (FK), TopicFilter, QoS, CreatedAt }
- **Message**: { Id (PK), Topic, Payload, QoS, Retain, Timestamp, ExpiryInterval }
- **RetainedMessage**: { Topic (PK), Payload, QoS, Timestamp }

## 技术实现细节
[本部分将在后续开发每一个模块/功能时，自动填充该模块/功能的技术实现方案、关键代码片段说明、API端点设计等。]

## 开发状态跟踪
| 模块/功能                | 状态     | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|--------------------------|----------|--------|--------------|--------------|------------|
| 项目结构初始化           | 未开始   | AI     | 2024-01-15   |              |            |
| 协议解析模块             | 未开始   | AI     | 2024-01-18   |              |            |
| 连接管理模块             | 未开始   | AI     | 2024-01-20   |              |            |
| 消息路由模块             | 未开始   | AI     | 2024-01-22   |              |            |
| QoS 处理模块             | 未开始   | AI     | 2024-01-25   |              |            |
| 会话管理模块             | 未开始   | AI     | 2024-01-27   |              |            |
| 认证授权模块             | 未开始   | AI     | 2024-01-29   |              |            |
| 消息持久化模块           | 未开始   | AI     | 2024-02-01   |              |            |
| 监控统计模块             | 未开始   | AI     | 2024-02-03   |              |            |
| 控制台演示程序           | 未开始   | AI     | 2024-02-05   |              |            |
| 单元测试                 | 未开始   | AI     | 2024-02-08   |              |            |
| 集成测试                 | 未开始   | AI     | 2024-02-10   |              |            |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

## 环境设置与运行指南
### 开发环境要求
- .NET 8.0 SDK 或更高版本
- Visual Studio 2022 或 Visual Studio Code
- SQL Server LocalDB 或 SQLite（用于开发测试）

### 运行步骤
1. 克隆项目到本地
2. 恢复 NuGet 包：`dotnet restore`
3. 更新数据库：`dotnet ef database update`
4. 运行服务器：`dotnet run --project src/MqttBroker.Server`
5. 运行控制台演示：`dotnet run --project src/MqttBroker.Console`

### 测试运行
- 运行所有测试：`dotnet test`
- 运行特定测试项目：`dotnet test tests/MqttBroker.Core.Tests`

## 性能目标
- 支持并发连接数：10,000+
- 消息吞吐量：100,000 消息/秒
- 内存使用：< 1GB（10,000 连接）
- 响应延迟：< 10ms（局域网环境）

## 部署指南
[将包含 Docker 容器化部署、Windows 服务部署、Linux 系统服务部署等相关说明。]
